import sharp from 'sharp';

export interface ResizeOptions {
  pixelWidth: number;
  pixelHeight: number;
  scaleFactor: number;
  pixelsPerMm: number;
  preserveAspectRatio?: boolean;
  kernel?: 'nearest' | 'cubic' | 'lanczos2' | 'lanczos3';
}

export interface JewelryDimensions {
  width: number;
  height: number;
  realWidthMm: number;
  realHeightMm: number;
}

export class JewelryResizer {
  constructor() {}

  /**
   * Resize jewelry with precise geometry calculations
   * Uses high-quality lanczos3 kernel for sharp visuals
   */
  async resizeJewelryPrecisely(
    jewelryBuffer: Buffer,
    options: ResizeOptions
  ): Promise<Buffer> {
    try {
      const { pixelWidth, pixelHeight, kernel = 'lanczos3', preserveAspectRatio = true } = options;

      console.log(`Resizing jewelry to ${pixelWidth}x${pixelHeight} pixels using ${kernel} kernel`);

      let resizeOptions: sharp.ResizeOptions = {
        width: pixelWidth,
        height: pixelHeight,
        kernel: kernel,
        fit: preserveAspectRatio ? 'inside' : 'fill',
        withoutEnlargement: false,
        fastShrinkOnLoad: false
      };

      const resizedJewelry = await sharp(jewelryBuffer)
        .resize(resizeOptions)
        .png({ quality: 100, compressionLevel: 0 }) // Maximum quality
        .toBuffer();

      console.log('Jewelry resized successfully with high quality');
      return resizedJewelry;

    } catch (error) {
      console.error('Error resizing jewelry:', error);
      throw new Error(`Failed to resize jewelry: ${error}`);
    }
  }

  /**
   * Calculate optimal dimensions based on jewelry type and body measurements
   */
  calculateOptimalDimensions(
    jewelryType: string,
    originalDimensions: JewelryDimensions,
    bodyMeasurements: { pixelsPerMm: number; referenceSize: number }
  ): ResizeOptions {
    const { pixelsPerMm, referenceSize } = bodyMeasurements;
    const { realWidthMm, realHeightMm } = originalDimensions;

    // Calculate precise pixel dimensions
    const pixelWidth = Math.round(realWidthMm * pixelsPerMm);
    const pixelHeight = Math.round(realHeightMm * pixelsPerMm);
    const scaleFactor = pixelWidth / originalDimensions.width;

    // Adjust for jewelry type specific requirements
    const adjustedDimensions = this.adjustForJewelryType(
      jewelryType,
      { pixelWidth, pixelHeight, scaleFactor, pixelsPerMm }
    );

    return {
      ...adjustedDimensions,
      preserveAspectRatio: true,
      kernel: 'lanczos3'
    };
  }

  /**
   * Apply jewelry-specific adjustments to dimensions
   */
  private adjustForJewelryType(
    jewelryType: string,
    baseDimensions: { pixelWidth: number; pixelHeight: number; scaleFactor: number; pixelsPerMm: number }
  ): ResizeOptions {
    const { pixelWidth, pixelHeight, scaleFactor, pixelsPerMm } = baseDimensions;

    switch (jewelryType.toLowerCase()) {
      case 'earrings':
        // Earrings should be slightly smaller to look natural
        return {
          pixelWidth: Math.round(pixelWidth * 0.9),
          pixelHeight: Math.round(pixelHeight * 0.9),
          scaleFactor: scaleFactor * 0.9,
          pixelsPerMm
        };

      case 'necklace':
        // Necklaces can maintain full size
        return {
          pixelWidth,
          pixelHeight,
          scaleFactor,
          pixelsPerMm
        };

      case 'ring':
        // Rings should be precise to finger size
        return {
          pixelWidth: Math.round(pixelWidth * 1.1), // Slightly larger for visibility
          pixelHeight: Math.round(pixelHeight * 1.1),
          scaleFactor: scaleFactor * 1.1,
          pixelsPerMm
        };

      case 'bracelet':
        // Bracelets should fit wrist proportions
        return {
          pixelWidth,
          pixelHeight: Math.round(pixelHeight * 0.8), // Thinner height
          scaleFactor,
          pixelsPerMm
        };

      default:
        return {
          pixelWidth,
          pixelHeight,
          scaleFactor,
          pixelsPerMm
        };
    }
  }

  /**
   * Enhance jewelry image quality before resizing
   */
  async preprocessJewelry(jewelryBuffer: Buffer): Promise<Buffer> {
    try {
      return await sharp(jewelryBuffer)
        .sharpen({ sigma: 1, m1: 1, m2: 2, x1: 2, y2: 10, y3: 20 }) // Enhance edges
        .modulate({ brightness: 1.05, saturation: 1.1 }) // Slight enhancement
        .png({ quality: 100 })
        .toBuffer();
    } catch (error) {
      console.error('Error preprocessing jewelry:', error);
      return jewelryBuffer; // Return original if preprocessing fails
    }
  }

  /**
   * Create a high-quality alpha mask for the jewelry
   */
  async createAlphaMask(jewelryBuffer: Buffer): Promise<Buffer> {
    try {
      // Extract alpha channel and enhance it
      const mask = await sharp(jewelryBuffer)
        .extractChannel('alpha')
        .blur(0.5) // Slight blur for natural edges
        .normalise() // Enhance contrast
        .toBuffer();

      return mask;
    } catch (error) {
      console.error('Error creating alpha mask:', error);
      throw new Error(`Failed to create alpha mask: ${error}`);
    }
  }

  /**
   * Validate jewelry image format and quality
   */
  async validateJewelryImage(jewelryBuffer: Buffer): Promise<{
    isValid: boolean;
    hasAlpha: boolean;
    dimensions: { width: number; height: number };
    format: string;
  }> {
    try {
      const metadata = await sharp(jewelryBuffer).metadata();
      
      return {
        isValid: metadata.width !== undefined && metadata.height !== undefined,
        hasAlpha: metadata.channels === 4,
        dimensions: { width: metadata.width || 0, height: metadata.height || 0 },
        format: metadata.format || 'unknown'
      };
    } catch (error) {
      console.error('Error validating jewelry image:', error);
      return {
        isValid: false,
        hasAlpha: false,
        dimensions: { width: 0, height: 0 },
        format: 'unknown'
      };
    }
  }
}
