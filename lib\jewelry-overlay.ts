import sharp from 'sharp';

export interface OverlayPosition {
  x: number;
  y: number;
  rotation?: number;
  scale?: number;
}

export interface ShadowOptions {
  enabled: boolean;
  offsetX: number;
  offsetY: number;
  blur: number;
  opacity: number;
  color: string;
}

export interface BlendingOptions {
  mode: 'over' | 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light' | 'add' | 'saturate';
  opacity: number;
  featherEdges: boolean;
  featherRadius: number;
}

export interface OverlayOptions {
  position: OverlayPosition;
  shadow?: ShadowOptions;
  blending?: BlendingOptions;
  enhanceRealism: boolean;
}

export class JewelryOverlay {
  constructor() { }

  /**
   * Overlay jewelry with precise positioning, shadows, and natural blending
   */
  async overlayJewelryWithEffects(
    baseImage: Buffer,
    jewelryImage: Buffer,
    options: OverlayOptions
  ): Promise<Buffer> {
    try {
      const { position, shadow, blending, enhanceRealism } = options;

      console.log(`Overlaying jewelry at position (${position.x}, ${position.y})`);

      // Step 1: Prepare the jewelry image with effects
      let processedJewelry = jewelryImage;

      if (enhanceRealism) {
        processedJewelry = await this.enhanceJewelryRealism(processedJewelry);
      }

      if (blending?.featherEdges) {
        processedJewelry = await this.featherEdges(processedJewelry, blending.featherRadius);
      }

      // Step 2: Create shadow if enabled
      let shadowLayer: Buffer | null = null;
      if (shadow?.enabled) {
        shadowLayer = await this.createShadow(processedJewelry, shadow);
      }

      // Step 3: Apply rotation if specified
      if (position.rotation && position.rotation !== 0) {
        processedJewelry = await this.rotateJewelry(processedJewelry, position.rotation);
        if (shadowLayer) {
          shadowLayer = await this.rotateJewelry(shadowLayer, position.rotation);
        }
      }

      // Step 4: Composite the final image
      const compositeOperations: sharp.OverlayOptions[] = [];

      // Add shadow first (behind jewelry)
      if (shadowLayer) {
        compositeOperations.push({
          input: shadowLayer,
          left: Math.round(position.x + (shadow?.offsetX || 0)),
          top: Math.round(position.y + (shadow?.offsetY || 0)),
          blend: 'multiply'
        });
      }

      // Add jewelry on top - use 'over' which is the default blend mode in Sharp
      const blendMode = this.validateBlendMode(blending?.mode || 'over');
      compositeOperations.push({
        input: processedJewelry,
        left: Math.round(position.x),
        top: Math.round(position.y),
        blend: blendMode
      });

      const finalImage = await sharp(baseImage)
        .composite(compositeOperations)
        .png({ quality: 100 })
        .toBuffer();

      console.log('Jewelry overlay completed successfully');
      return finalImage;

    } catch (error) {
      console.error('Error overlaying jewelry:', error);
      throw new Error(`Failed to overlay jewelry: ${error}`);
    }
  }

  /**
   * Create a natural shadow for the jewelry
   */
  private async createShadow(
    jewelryImage: Buffer,
    shadowOptions: ShadowOptions
  ): Promise<Buffer> {
    try {
      const { blur, opacity, color } = shadowOptions;

      // Extract alpha channel to create shadow shape
      const shadowShape = await sharp(jewelryImage)
        .extractChannel('alpha')
        .blur(blur)
        .toBuffer();

      // Create colored shadow
      const shadowColor = this.hexToRgb(color);
      const shadow = await sharp({
        create: {
          width: (await sharp(jewelryImage).metadata()).width!,
          height: (await sharp(jewelryImage).metadata()).height!,
          channels: 4,
          background: { r: shadowColor.r, g: shadowColor.g, b: shadowColor.b, alpha: opacity }
        }
      })
        .composite([{
          input: shadowShape,
          blend: 'dest-in'
        }])
        .png()
        .toBuffer();

      return shadow;

    } catch (error) {
      console.error('Error creating shadow:', error);
      throw new Error(`Failed to create shadow: ${error}`);
    }
  }

  /**
   * Enhance jewelry realism with lighting and material effects
   */
  private async enhanceJewelryRealism(jewelryImage: Buffer): Promise<Buffer> {
    try {
      return await sharp(jewelryImage)
        .modulate({
          brightness: 1.1,    // Slight brightness boost
          saturation: 1.15,   // Enhanced colors
          hue: 0
        })
        .sharpen({
          sigma: 1,
          m1: 1,
          m2: 2,
          x1: 2,
          y2: 10,
          y3: 20
        })
        .png({ quality: 100 })
        .toBuffer();

    } catch (error) {
      console.error('Error enhancing jewelry realism:', error);
      return jewelryImage;
    }
  }

  /**
   * Feather the edges of jewelry for natural blending
   */
  private async featherEdges(jewelryImage: Buffer, radius: number): Promise<Buffer> {
    try {
      const metadata = await sharp(jewelryImage).metadata();

      // Create a feathered mask
      const mask = await sharp(jewelryImage)
        .extractChannel('alpha')
        .blur(radius)
        .toBuffer();

      // Apply the feathered mask
      return await sharp(jewelryImage)
        .composite([{
          input: mask,
          blend: 'dest-in'
        }])
        .png()
        .toBuffer();

    } catch (error) {
      console.error('Error feathering edges:', error);
      return jewelryImage;
    }
  }

  /**
   * Rotate jewelry image around its center
   */
  private async rotateJewelry(jewelryImage: Buffer, angle: number): Promise<Buffer> {
    try {
      return await sharp(jewelryImage)
        .rotate(angle, { background: { r: 0, g: 0, b: 0, alpha: 0 } })
        .png()
        .toBuffer();

    } catch (error) {
      console.error('Error rotating jewelry:', error);
      return jewelryImage;
    }
  }

  /**
   * Calculate optimal overlay position based on landmarks and jewelry type
   */
  calculateOverlayPosition(
    landmarks: any,
    jewelryType: string,
    jewelryDimensions: { width: number; height: number }
  ): OverlayPosition {
    switch (jewelryType.toLowerCase()) {
      case 'earrings':
        return this.calculateEarringPosition(landmarks, jewelryDimensions);
      case 'necklace':
        return this.calculateNecklacePosition(landmarks, jewelryDimensions);
      case 'ring':
        return this.calculateRingPosition(landmarks, jewelryDimensions);
      case 'bracelet':
        return this.calculateBraceletPosition(landmarks, jewelryDimensions);
      default:
        return { x: 0, y: 0, rotation: 0, scale: 1 };
    }
  }

  private calculateEarringPosition(landmarks: any, dimensions: { width: number; height: number }): OverlayPosition {
    // Position earrings at ear landmarks
    const leftEar = landmarks.leftEar || landmarks.landmarks[0];
    return {
      x: leftEar.x - dimensions.width / 2,
      y: leftEar.y - dimensions.height / 2,
      rotation: landmarks.faceAngle || 0,
      scale: 1
    };
  }

  private calculateNecklacePosition(landmarks: any, dimensions: { width: number; height: number }): OverlayPosition {
    // Position necklace at neck area
    const neckCenter = landmarks.landmarks[7] || { x: landmarks.width / 2, y: landmarks.height * 0.6 };
    return {
      x: neckCenter.x - dimensions.width / 2,
      y: neckCenter.y - dimensions.height / 3,
      rotation: 0,
      scale: 1
    };
  }

  private calculateRingPosition(landmarks: any, dimensions: { width: number; height: number }): OverlayPosition {
    // Position ring on finger
    const fingerPos = landmarks.landmarks[4] || { x: landmarks.width * 0.5, y: landmarks.height * 0.7 };
    return {
      x: fingerPos.x - dimensions.width / 2,
      y: fingerPos.y - dimensions.height / 2,
      rotation: 0,
      scale: 1
    };
  }

  private calculateBraceletPosition(landmarks: any, dimensions: { width: number; height: number }): OverlayPosition {
    // Position bracelet on wrist
    const wristPos = landmarks.landmarks[0] || { x: landmarks.width * 0.5, y: landmarks.height * 0.85 };
    return {
      x: wristPos.x - dimensions.width / 2,
      y: wristPos.y - dimensions.height / 2,
      rotation: 0,
      scale: 1
    };
  }

  /**
   * Create default shadow options for jewelry type
   */
  createDefaultShadowOptions(jewelryType: string): ShadowOptions {
    const baseOptions = {
      enabled: true,
      offsetX: 2,
      offsetY: 2,
      blur: 3,
      opacity: 0.3,
      color: '#000000'
    };

    switch (jewelryType.toLowerCase()) {
      case 'earrings':
        return { ...baseOptions, offsetX: 1, offsetY: 2, blur: 2, opacity: 0.25 };
      case 'necklace':
        return { ...baseOptions, offsetX: 0, offsetY: 3, blur: 4, opacity: 0.2 };
      case 'ring':
        return { ...baseOptions, offsetX: 1, offsetY: 1, blur: 1, opacity: 0.4 };
      case 'bracelet':
        return { ...baseOptions, offsetX: 2, offsetY: 3, blur: 3, opacity: 0.3 };
      default:
        return baseOptions;
    }
  }

  /**
   * Create default blending options for natural appearance
   */
  createDefaultBlendingOptions(): BlendingOptions {
    return {
      mode: 'over',
      opacity: 1.0,
      featherEdges: true,
      featherRadius: 1
    };
  }

  /**
   * Validate and return a safe blend mode for Sharp.js
   */
  private validateBlendMode(mode: string): string {
    const validModes = ['over', 'multiply', 'screen', 'overlay', 'soft-light', 'hard-light', 'add', 'saturate'];
    return validModes.includes(mode) ? mode : 'over';
  }

  /**
   * Utility function to convert hex color to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  }
}
